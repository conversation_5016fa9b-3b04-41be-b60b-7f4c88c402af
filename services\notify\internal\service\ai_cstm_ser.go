package service

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"52tt.com/cicd/pkg/dify"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/services/notify/internal/conf"
	"github.com/google/uuid"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	"github.com/larksuite/oapi-sdk-go/v3/event/dispatcher"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	larkws "github.com/larksuite/oapi-sdk-go/v3/ws"
)

type AICstmSerSvc interface {
	Init()
}

// AI 智能客服
type AICstmSer struct {
	mode    string
	cfg     conf.CustomerService
	cli     *lark.Client
	difyCli dify.Service
}

func NewAICstmSer(cfg conf.CustomerService, mode string, difyCli dify.Service) AICstmSerSvc {
	return &AICstmSer{
		mode:    mode,
		cfg:     cfg,
		difyCli: difyCli,
	}
}

func (svc *AICstmSer) Init() {
	logMode := larkcore.LogLevelDebug
	if svc.mode == "release" {
		logMode = larkcore.LogLevelWarn
	}
	fsSecret := os.Getenv("fsSecret")
	svc.cli = lark.NewClient(svc.cfg.AppID, fsSecret, lark.WithLogReqAtDebug(true), lark.WithLogLevel(logMode))

	// 注册事件回调，OnP2MessageReceiveV1 为接收消息 v2.0；OnCustomizedEvent 内的 message 为接收消息 v1.0。
	eventHandler := dispatcher.NewEventDispatcher(svc.cfg.VerfToken, "").
		OnP2MessageReceiveV1(func(ctx context.Context, event *larkim.P2MessageReceiveV1) error {
			// 长连接模式下接收到消息后，需要在 3 秒内处理完成，否则会触发超时重推机制
			req := larkim.NewCreateMessageReactionReqBuilder().
				MessageId(*event.Event.Message.MessageId).
				Body(larkim.NewCreateMessageReactionReqBodyBuilder().
					ReactionType(larkim.NewEmojiBuilder().
						EmojiType(`THINKING`).
						Build()).
					Build()).
				Build()

			// 发起请求
			_, err := svc.cli.Im.MessageReaction.Create(context.Background(), req)
			// 发送结果处理，resp,err
			if err != nil {
				log.ErrorWithCtx(ctx, "Im.Message.Create err %v", err)
				return err
			}
			go svc.dealMsg(ctx, event)

			return nil
		})
		// OnCustomizedEvent("这里填入你要自定义订阅的 event 的 key，例如 out_approval", func(ctx context.Context, event *larkevent.EventReq) error {
		// 	fmt.Printf("[ OnCustomizedEvent access ], type: message, data: %s\n", string(event.Body))
		// 	return nil
		// })
	// 创建Client
	cli := larkws.NewClient(svc.cfg.AppID, fsSecret,
		larkws.WithEventHandler(eventHandler),
		larkws.WithLogLevel(larkcore.LogLevelDebug),
	)

	// 启动客户端
	err := cli.Start(context.Background())
	if err != nil {
		panic(err)
	}
}

func (svc *AICstmSer) dealMsg(ctx context.Context, event *larkim.P2MessageReceiveV1) {
	defer func() {
		if r := recover(); r != nil {
			log.ErrorWithCtx(ctx, "AICstmSer.dealMsg Panic %v", r)
		}
	}()

	msg := svc.parsMsg(event)
	// 处理消息
	// fmt.Printf("[ OnP2MessageReceiveV1 access ], data: %s\n", larkcore.Prettify(event))
	resp, err := svc.difyCli.Chat(ctx, dify.ChatRep{
		Query:        msg,
		ResponseMode: "blocking",
		User:         *event.Event.Sender.SenderId.OpenId,
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "difyCli.Chat err %v", err)
		return
	}

	var req *larkim.CreateMessageReq
	switch *event.Event.Message.ChatType {
	case "p2p":
		respMsg := TxtMsg{Text: resp.Answer}
		req = larkim.NewCreateMessageReqBuilder().
			ReceiveIdType(`open_id`).
			Body(larkim.NewCreateMessageReqBodyBuilder().
				ReceiveId(*event.Event.Sender.SenderId.OpenId).
				MsgType(`text`).
				Content(toJson(respMsg)).
				Uuid(uuid.New().String()).
				Build()).
			Build()
	case "group":
		respMsg := TxtMsg{Text: fmt.Sprintf(`<at user_id="%s"></at>%s`, *event.Event.Sender.SenderId.OpenId, resp.Answer)}
		req = larkim.NewCreateMessageReqBuilder().
			ReceiveIdType(`chat_id`).
			Body(larkim.NewCreateMessageReqBodyBuilder().
				ReceiveId(*event.Event.Message.ChatId).
				MsgType(`text`).
				Content(toJson(respMsg)).
				Uuid(uuid.New().String()).
				Build()).
			Build()

	}
	_, err = svc.cli.Im.Message.Create(context.Background(), req)
	if err != nil {
		log.ErrorWithCtx(ctx, "Im.Message.Create err %v", err)
		return
	}

}

func (svc *AICstmSer) parsMsg(event *larkim.P2MessageReceiveV1) (msg string) {
	msgContent := event.Event.Message.Content
	if msgContent == nil {
		return
	}

	switch *event.Event.Message.MessageType {
	case "text":
		msg = jsonTo[TxtMsg](*msgContent).Text
	case "file":

	}

	for _, mention := range event.Event.Message.Mentions {
		if mention == nil {
			continue
		}
		msg = strings.Replace(msg, *mention.Key, "", -1)
	}

	return
}

type TxtMsg struct {
	Text string `json:"text"`
}

type FileMsg struct {
	FileKey  string `json:"file_key"`
	FileName string `json:"file_name"`
}

func toJson[T any](t T) string {
	by, _ := json.Marshal(t)
	return string(by)
}

func jsonTo[T any](str string) T {
	var t T
	_ = json.Unmarshal([]byte(str), &t)
	return t
}
