package tools

import (
	"reflect"
	"testing"

	"github.com/golang/protobuf/ptypes/struct"
)

type MyStruct struct {
	Key1 string `json:"key1"`
	Key2 string `json:"key2"`
}

func TestConvertStructPbToStruct(t *testing.T) {
	src := &structpb.Struct{
		Fields: map[string]*structpb.Value{
			"key1": {Kind: &structpb.Value_StringValue{StringValue: "value1"}},
			"key2": {Kind: &structpb.Value_StringValue{StringValue: "value2"}},
		},
	}

	expected := &MyStruct{}
	err := ConvertStructPbToStruct(src, expected)
	if err != nil {
		t.Errorf("ConvertStructPbToStruct returned an error: %v", err)
	}

	// 检查转换后的结构体内容是否正确
	expectedResult := &MyStruct{Key1: "value1", Key2: "value2"}
	if !reflect.DeepEqual(expected, expectedResult) {
		t.Errorf("Converted struct does not match the expected result. Got: %+v, Expected: %+v", expected, expectedResult)
	}
}

func TestConvertStructPbToStruct_ErrorCase(t *testing.T) {
	src := &structpb.Struct{
		Fields: map[string]*structpb.Value{},
	}

	dest := &MyStruct{}
	err := ConvertStructPbToStruct(src, dest)
	if err == nil {
		t.Error("Expected an error but got nil")
	}
}
