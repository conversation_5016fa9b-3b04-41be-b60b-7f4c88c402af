package tools

import (
	"encoding/json"
	"github.com/golang/protobuf/jsonpb"
	"github.com/golang/protobuf/ptypes/struct"
)

func ConvertStructPbToStruct(src *structpb.Struct, dest interface{}) error {
	jsonStr, err := (&jsonpb.Marshaler{}).MarshalToString(src)
	if err != nil {
		return err
	}

	err = json.Unmarshal([]byte(jsonStr), dest)
	if err != nil {
		return err
	}

	return nil
}

func ConvertStructToBytes(s *structpb.Struct) ([]byte, error) {
	m := jsonpb.Marshaler{}
	jsonStr, err := m.MarshalToString(s)
	if err != nil {
		return nil, err
	}

	return []byte(jsonStr), nil
}
