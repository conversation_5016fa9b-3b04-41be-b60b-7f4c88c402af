package service

import (
	"fmt"
	"testing"

	"52tt.com/cicd/pkg/constants"
	mpkg "52tt.com/cicd/pkg/lark"
	"52tt.com/cicd/services/notify/pkg"
	"github.com/mitchellh/mapstructure"
)

func TestSendInteractive2(t *testing.T) {
	t.Skip("忽略")
	testApproval := mpkg.CardContent{
		BackGroundColor:  "blue",
		TicketName:       "工单名称",
		ReasonForChange:  "sss",
		CardType:         constants.TESTACCEPTANCEUPDATETICKET.String(),
		TicketType:       "测试验收工单",
		TicketNo:         "No_68723",
		Applicant:        "名字",
		ApplicationTime:  "2023-5-30 14:20:35 星期二 CST",
		ApprovalNode:     "114433",
		Approve:          "ლ(′◉❥◉｀ლ)",
		ServiceName:      "pipeline-service",
		Branch:           "master",
		Env:              "sub",
		Clusters:         "k8s-dev-oggyhufrty34fw2/devops",
		Detail:           "https://baidu.com",
		ExpireDate:       "sdfsdf",
		IsRequiredReport: "false",
		PipelineName:     "pipeline-1",
		CheckReport:      "http://",
		TicketTypeEn:     constants.TestAcceptanceTicket.String(),
	}

	//获取飞书client
	client := mpkg.NewClient("cli_a4f5adb0e2b9d00d", "m4rAc6pGodqPplQMZOTM30PVl0FBO0NJ")
	m := make(map[string]string)

	err := mapstructure.Decode(testApproval, &m)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(m, m["card_type"])
	m["is_stage_check_str"] = "是"
	updateTicketContent := pkg.GetRealContent(m, m["card_type"])
	fmt.Println("updateTicketContent:", updateTicketContent)
	_, err = client.SendMsgToUsr(updateTicketContent, "on_5fe72a9ddf6a2e1ba717ce0b7919ade2", "interactive")
	if err != nil {
		fmt.Println(err)
		return
	}
}

func TestSendInteractive(t *testing.T) {
	t.Skip("忽略 发给hzx的")
	testApproval := mpkg.CardContent{
		BackGroundColor:  "blue",
		TicketName:       "工单名称",
		ReasonForChange:  "sss",
		CardType:         constants.UPGRADETICKET.String(),
		TicketType:       "测试验收工单",
		TicketNo:         "No_68723",
		Applicant:        "名字",
		ApplicationTime:  "2023-5-30 14:20:35 星期二 CST",
		ApprovalNode:     "114433",
		Approve:          "ლ(′◉❥◉｀ლ)",
		ServiceName:      "pipeline-service",
		Branch:           "master",
		Env:              "sub",
		Clusters:         "k8s-dev-oggyhufrty34fw2/devops",
		Detail:           "https://baidu.com",
		ExpireDate:       "sdfsdf",
		IsRequiredReport: "false",
		PipelineName:     "pipeline-1",
		CheckReport:      "http://",
		TicketTypeEn:     constants.UPGRADETICKET.String(),
	}

	//获取飞书client
	client := mpkg.NewClient("cli_a4f5adb0e2b9d00d", "m4rAc6pGodqPplQMZOTM30PVl0FBO0NJ")
	m := make(map[string]string)

	err := mapstructure.Decode(testApproval, &m)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(m, m["card_type"])
	updateTicketContent := pkg.GetRealContent(m, m["card_type"])
	fmt.Println("updateTicketContent:", updateTicketContent)
	_, err = client.SendMsgToUsr(updateTicketContent, "on_d14c20b4a0dc166ffc9d5c8896399852", "interactive")
	if err != nil {
		fmt.Println(err)
		return
	}
}
